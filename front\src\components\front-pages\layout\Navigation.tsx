import { Link, useLocation } from "react-router";
const FrontNav = [
  {
    menu: "عن العميرة",
    link: "/about",
    badge: false,
  },
  {
    menu: "استورد معنا",
    link: "/import",
    badge: false,
  },
  {
    menu: "اقسام المنتجات",
    link: "/shop",
    badge: false,
  },
  {
    menu: "الرئيسية",
    link: "/",
    badge: false,
  },
];

const Navigation = () => {
  const location = useLocation();
  const pathname = location.pathname;
  return (
    <>
      <ul className="flex xl:flex-row flex-col xl:gap-9 gap-6 xl:items-center">
        {FrontNav.map((item, index) => (
          <li
            key={index}
            className={`rounded-full font-semibold text-15 py-1.5 px-2.5 ${pathname == item.link ? 'bg-lightprimary text-primaryemphasis' : 'text-dark dark:text-white ' }`}
          >
            <Link to={item.link} className="flex gap-3 items-center text-primary-ld">
              {item.menu}
            </Link>
          </li>
        ))}
      </ul>
    </>
  );
};

export default Navigation;
