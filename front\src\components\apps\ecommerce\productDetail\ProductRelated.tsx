


import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ing<PERSON><PERSON>, Toolt<PERSON> } from "flowbite-react";
import { Icon } from "@iconify/react";
import { Link } from "react-router";
import ProductsData from "src/api/eCommerce/ProductsData";
import CardBox from "src/components/shared/CardBox";
const ProductRelated = () => {
  return (
    <>
      <div className="mt-10">
        <h5 className="text-xl">Related Products</h5>
        <div className="grid grid-cols-12 gap-5 mt-4">
          {ProductsData.map((product) => (
            <>
              {product.related == true ? (
                <div
                  className="lg:col-span-3 md:col-span-6 col-span-12"
                  key={product.id}
                >
                  <CardBox className="p-0 overflow-hidden group card-hover group">
                    <div className="relative">
                      <Link to={`/apps/blog/detail/${product.id}`}>
                        <div className="overflow-hidden h-[265px]">
                          <img
                            src={product.photo}
                            alt="MatDash"
                            height={265}
                            width={500}
                            className="w-full object-cover h-full"
                          />
                        </div>
                        <div className="p-6 pt-4">
                          <div className="flex justify-between items-center -mt-8 ">
                            <div className="ms-auto">
                              <Tooltip content={"Add To Cart"} className="">
                                <Button
                                  color={"primary"}
                                  className="btn-circle ms-auto h-8 w-8 p-0 rounded-full!"
                                >
                                  <Icon icon='solar:cart-3-outline' height={18} />
                                </Button>
                              </Tooltip>
                            </div>
                          </div>
                          <h6 className="text-base line-clamp-1 group-hover:text-primary">
                            {product.title}
                          </h6>
                          <div className="flex justify-between items-center mt-1">
                            <h5 className="text-base flex gap-2 items-center">
                              ${product.price}{" "}
                              <span className="font-normal text-sm text-darklink dark:text-bodytext line-through">
                                ${product.salesPrice}
                              </span>
                            </h5>
                            {product.rating == 5 ? (
                              <Rating size={"sm"}>
                                <RatingStar />
                                <RatingStar />
                                <RatingStar />
                                <RatingStar />
                                <RatingStar />
                              </Rating>
                            ) : product.rating == 4 ? (
                              <Rating size={"sm"}>
                                <RatingStar />
                                <RatingStar />
                                <RatingStar />
                                <RatingStar />
                                <RatingStar filled={false} />
                              </Rating>
                            ) : (
                              <Rating size={"sm"}>
                                <RatingStar />
                                <RatingStar />
                                <RatingStar />
                                <RatingStar filled={false} />
                                <RatingStar filled={false} />
                              </Rating>
                            )}
                          </div>
                        </div>
                      </Link>
                    </div>
                  </CardBox>
                </div>
              ) : null}
            </>
          ))}
        </div>
      </div>
    </>
  );
};

export default ProductRelated;
