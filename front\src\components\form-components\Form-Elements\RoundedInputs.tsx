
import CardBox from "src/components/shared/CardBox";
import { Label, TextInput, Checkbox, Button } from "flowbite-react";
import RoundInputsCodes from "./Codes/RoundedInputs";

const RoundedInputs = () => {
  return (
<div>
  <CardBox>
    <div className="flex justify-between">
      <h4 className="text-lg font-semibold mb-2">Rounded Inputs form</h4>
      <RoundInputsCodes />
    </div>

    <form className="flex flex-col gap-4">
      <div>
        <Label htmlFor="email1">Your email</Label>
        <TextInput
          id="email1"
          type="email"
          placeholder="<EMAIL>"
          required
          className="form-control-rounded"
        />
      </div>
      <div>
        <Label htmlFor="password1">Your password</Label>
        <TextInput
          id="password1"
          type="password"
          required
          className="form-control-rounded"
        />
      </div>
      <div className="flex items-center gap-2">
        <Checkbox className="checkbox" id="remember" />
        <Label htmlFor="remember">Remember me</Label>
      </div>
      <Button type="submit" color="primary">
        Submit
      </Button>
    </form>
  </CardBox>
</div>

  );
};

export default RoundedInputs;
