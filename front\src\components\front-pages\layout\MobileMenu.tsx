import { useState } from "react";
import { <PERSON><PERSON>, Drawer, DrawerItems } from "flowbite-react";
import { IconMenu2 } from "@tabler/icons-react";
import FullLogo from "src/layouts/full/shared/logo/FullLogo";
import { Link, useLocation } from "react-router";
import { useUser } from "src/context/UserContext";
import { Icon } from "@iconify/react";

const MobileMenu = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { user, logout } = useUser();

  const handleClose = () => setIsOpen(false);
  
  const handleLogout = () => {
    logout();
    handleClose();
  };

  const FrontNav = [
    {
      menu: "الرئيسية",
      link: "/",
      badge: false,
    },
    {
      menu: "اقسام المنتجات",
      link: "/shop",
      badge: true,
    },
    {
      menu: "استورد معنا",
      link: "/import",
      badge: false,
    },
    {
      menu: "عن العميرة",
      link: "/about",
      badge: false,
    },
  ];

  // Customer menu items for mobile
  const customerMenuItems = [
    {
      href: "/customer/profile",
      icon: "solar:user-circle-bold-duotone",
      title: "ملفي الشخصي",
    },
    {
      href: "/customer/orders",
      icon: "solar:bag-3-line-duotone",
      title: "طلباتي",
    },
    {
      href: "/customer/settings",
      icon: "solar:settings-line-duotone",
      title: "الإعدادات",
    }
  ];

  const location = useLocation();
  const pathname = location.pathname;

  return (
    <>
      <div className="xl:hidden flex">
        <Button
          onClick={() => setIsOpen(true)}
          className="flex items-center justify-center text-dark dark:text-white p-0 h-10 w-10 rounded-full bg-transparent hover:bg-lightprimary"
        >
          <IconMenu2 />
        </Button>
      </div>
      <Drawer open={isOpen} onClose={handleClose} className="h-full">
        <DrawerItems className="p-6">
          <div className="mb-6">
            <FullLogo />
          </div>
          
          {/* User Profile Section */}
          {user && (
            <div className="mb-6 pb-6 border-b border-border dark:border-darkborder">
              <div className="flex items-center gap-3">
                {user.profile_picture_url ? (
                  <img src={user.profile_picture_url} alt="profile" height="50" width="50" className="rounded-full object-cover" />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-primary text-white flex items-center justify-center text-lg font-semibold">
                    {user.name?.charAt(0).toUpperCase()}
                  </div>
                )}
                <div>
                  <h5 className="font-semibold">{user.name}</h5>
                  <p className="text-sm text-gray-500">{user.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Items */}
          <ul className="flex xl:flex-row flex-col xl:gap-2 gap-2 xl:items-center">
            {FrontNav.map((item, index) => (
              <li
                key={index}
                className={`rounded-full font-semibold text-15 py-3 px-5 ${
                  pathname == item.link ? 'bg-lightprimary text-primary' : 'text-dark dark:text-white'
                }`}
              >
                <Link to={item.link} onClick={handleClose} className="flex gap-3 items-center text-primary-ld">
                  {item.menu}
                </Link>
              </li>
            ))}
          </ul>

          {/* Customer Menu Items - Only shown when user is logged in */}
          {user && (
            <>
              <div className="mt-6 pt-6 border-t border-border dark:border-darkborder">
                <h6 className="text-sm font-semibold text-gray-500 mb-3">حسابي</h6>
                <ul className="flex flex-col gap-2">
                  {customerMenuItems.map((item, index) => (
                    <li key={index}>
                      <Link
                        to={item.href}
                        onClick={handleClose}
                        className="flex items-center gap-3 py-2 px-3 rounded-lg hover:bg-lightprimary hover:text-primary transition-colors"
                      >
                        <Icon icon={item.icon} height={20} />
                        <span className="text-sm font-medium">{item.title}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              
              <div className="mt-6">
                <Button
                  color={"primary"}
                  onClick={handleLogout}
                  className="w-full font-bold bg-primary hover:bg-primaryemphasis text-white"
                >
                  تسجيل الخروج
                </Button>
              </div>
            </>
          )}

          {/* Login Button - Only shown when user is not logged in */}
          {!user && (
            <Button
              as={Link}
              to="/customerLogin"
              onClick={handleClose}
              className="mt-6 w-full font-bold bg-primary hover:bg-primaryemphasis text-white"
              color={'sky'}
            >
              تسجيل الدخول
            </Button>
          )}
        </DrawerItems>
      </Drawer>
    </>
  );
};

export default MobileMenu;