import { Button } from 'flowbite-react';
import { Link } from 'react-router';
import banner from '/src/assets/images/front-pages/background/main-banner.webp';
import { useUser } from 'src/context/UserContext'; // <-- IMPORT THE HOOK

const MainBanner = () => {
  const { user } = useUser(); // <-- GET THE CURRENT USER

  return (
    <div className="bg-lightgray dark:bg-darkgray">
      <div className="container-1218 mx-auto ">
        <div className="grid grid-cols-12 gap-6 items-center lg:py-20 md:py-12 py-8">
          <div className="lg:col-span-6 col-span-12">
            {user ? (
              // --- CONTENT FOR LOGGED-IN USERS ---
              <>
                <h1 className="text-dark dark:text-white xl:text-display-1 lg:text-5xl text-4xl font-bold leading-tight">
                  Welcome back, {user.name}!
                </h1>
                <p className="text-lg text-darklink dark:text-bodytext lg:py-7 py-5">
                  You're logged in and ready to go. Explore your account or see what's new.
                </p>
                <div className="flex gap-4">
                  <Button as={Link} to="/customer/profile" color={'primary'} className="rounded-full">
                    My Account
                  </Button>
                  <Button as={Link} to="/products" color={'dark'} className="rounded-full">
                    Explore Products
                  </Button>
                </div>
              </>
            ) : (
              // --- CONTENT FOR GUESTS ---
              <>
                <h1 className="text-dark dark:text-white xl:text-display-1 lg:text-5xl text-4xl font-bold leading-tight">
                  Build your next great idea with Omera
                </h1>
                <p className="text-lg text-darklink dark:text-bodytext lg:py-7 py-5">
                  Omera helps developers to build organized and well-coded dashboards full of beautiful and rich modules.
                </p>
                <div className="flex gap-4">
                  <Button as={Link} to="/customerLogin" color={'primary'} className="rounded-full">
                    Login
                  </Button>
                  <Button as={Link} to="/customerRegister" color={'dark'} className="rounded-full">
                    Register
                  </Button>
                </div>
              </>
            )}
          </div>
          <div className="lg:col-span-6 col-span-12">
            <div className="lg:block hidden">
              <img src={banner} alt="banner" className="w-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MainBanner;