// Get the base URL from environment variables
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// This helper gets the actual data payload from the object SWR sends.
const getPayload = (arg: any) => (arg && arg.arg) ? arg.arg : arg;

const getFetcher = (url: string) =>
  fetch(`${API_BASE_URL}${url}`).then((res) => {
    if (!res.ok) {
      throw new Error('Failed to fetch the data');
    }
    return res.json();
  });

const postFetcher = (url: string, arg: any) =>
  fetch(`${API_BASE_URL}${url}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(getPayload(arg)), // Use helper to get correct payload
  }).then((res) => {
    if (!res.ok) {
      return res.json().then(errorBody => {
        throw new Error(errorBody.error || 'Failed to post data');
      });
    }
    return res.json();
  });

const putFetcher = (url: string, arg: any) =>
  fetch(`${API_BASE_URL}${url}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(getPayload(arg)), // Use helper here too
  }).then((res) => {
    if (!res.ok) {
      return res.json().then(errorBody => {
        throw new Error(errorBody.error || 'Failed to update data');
      });
    }
    return res.json();
  });

const deleteFetcher = (url: string) =>
  fetch(`${API_BASE_URL}${url}`, {
    method: 'DELETE',
    headers: { 'Content-Type': 'application/json' },
  }).then((res) => {
    if (!res.ok && res.status !== 204) {
      return res.json().then(errorBody => {
        throw new Error(errorBody.error || 'Failed to delete data');
      });
    }
    return res.status === 204 ? { success: true } : res.json();
  });

export { getFetcher, postFetcher, putFetcher, deleteFetcher };