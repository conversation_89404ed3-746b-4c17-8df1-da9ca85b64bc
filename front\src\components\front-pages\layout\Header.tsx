// src/components/front-pages/layout/Header.tsx
import { useState, useEffect, useContext } from 'react';
import { Button } from 'flowbite-react';
import Navigation from './Navigation';
import MobileMenu from './MobileMenu';
import FullLogo from 'src/layouts/full/shared/logo/FullLogo';
import { Link } from 'react-router';
import { useUser } from 'src/context/UserContext';
import { motion } from 'framer-motion';
import { PageContext } from 'src/context/PageContext';
import CustomerProfile from "src/views/customers/CustomerProfile";
import 'flowbite';

const FrontHeader = () => {
  const { user, logout } = useUser();
  const [isSticky, setIsSticky] = useState(false);
  const { isHeaderVisible } = useContext(PageContext) || { isHeaderVisible: true };

  useEffect(() => {
    const handleScroll = () => {
      // The sticky background effect should only trigger AFTER the header is visible
      // and the user has scrolled past the animation area.
      if (window.scrollY > window.innerHeight * 2) { // 200vh from StickyScrollImages
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <motion.header
      className="fixed top-0 w-full z-50"
      animate={{ y: isHeaderVisible ? 0 : '-100%' }}
      transition={{ duration: 0.5, ease: 'easeInOut' }}
    >
      <div
        className={`transition-colors ${
          isSticky
            ? 'bg-white dark:bg-dark shadow-md py-5'
            : 'bg-lightgray dark:bg-darkgray lg:py-9 py-5'
        }`}
      >
        <div className="container-1218 mx-auto flex justify-between items-center">
          <FullLogo />
          <div className="xl:block hidden">
            <Navigation />
          </div>
          <div className="xl:flex hidden items-center gap-4">
            {user ? (
              <div className="flex items-center gap-3">
                <CustomerProfile /> {/* Use the CustomerProfile component here */}
              </div>
            ) : (
              <Button 
                as={Link} 
                to="/customerLogin" 
                className="font-bold bg-primary hover:bg-primaryemphasis text-white" 
                color={'sky'}
              >
                تسجيل الدخول
              </Button>
            )}
          </div>
          <MobileMenu />
        </div>
      </div>
    </motion.header>
  );
};

export default FrontHeader;