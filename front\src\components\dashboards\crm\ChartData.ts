export const AreaChartData1: any = {
    chart: {
        type: 'area',
        width: 143,
        height: 14,
        sparkline: {
            enabled: true
        },
        group: 'table-chart',
        fontFamily: 'inherit',
        foreColor: '#adb0bb'
    },
    series: [
        {
            color: 'var(--color-success)',
            data: [25, 66, 20, 40, 12, 58, 20]
        }
    ],
    stroke: {
        curve: 'smooth',
        width: 2
    },
    fill: {
        type: 'gradient',
        gradient: {
            shadeIntensity: 0,
            inverseColors: false,
            opacityFrom: 0,
            opacityTo: 0,
            stops: [20, 180]
        }
    },
    markers: {
        size: 0
    },
    tooltip: {
        enabled: false
    }
}

export const AreaChartData2: any = {
    chart: {
        type: 'area',
        width: 143,
        height: 14,
        sparkline: {
            enabled: true
        },
        group: 'table-chart',
        fontFamily: 'inherit',
        foreColor: '#adb0bb'
    },
    series: [
        {
            color: 'var(--color-error)',
            data: [25, 66, 20, 40, 12, 58, 20]
        }
    ],
    stroke: {
        curve: 'smooth',
        width: 2
    },
    fill: {
        type: 'gradient',
        gradient: {
            shadeIntensity: 0,
            inverseColors: false,
            opacityFrom: 0,
            opacityTo: 0,
            stops: [20, 180]
        }
    },
    markers: {
        size: 0
    },
    tooltip: {
        enabled: false
    }
}

export const AreaChartData3: any = {
    chart: {
        type: 'area',
        width: 143,
        height: 14,
        sparkline: {
            enabled: true
        },
        group: 'table-chart',
        fontFamily: 'inherit',
        foreColor: '#adb0bb'
    },
    series: [
        {
            color: 'var(--color-warning)',
            data: [25, 66, 20, 40, 12, 58, 20]
        }
    ],
    stroke: {
        curve: 'smooth',
        width: 2
    },
    fill: {
        type: 'gradient',
        gradient: {
            shadeIntensity: 0,
            inverseColors: false,
            opacityFrom: 0,
            opacityTo: 0,
            stops: [20, 180]
        }
    },
    markers: {
        size: 0
    },
    tooltip: {
        enabled: false
    }
}

export const AreaChartData4: any = {
    chart: {
        type: 'area',
        width: 143,
        height: 14,
        sparkline: {
            enabled: true
        },
        group: 'table-chart',
        fontFamily: 'inherit',
        foreColor: '#adb0bb'
    },
    series: [
        {
            color: 'var(--color-secondary)',
            data: [25, 66, 20, 40, 12, 58, 20]
        }
    ],
    stroke: {
        curve: 'smooth',
        width: 2
    },
    fill: {
        type: 'gradient',
        gradient: {
            shadeIntensity: 0,
            inverseColors: false,
            opacityFrom: 0,
            opacityTo: 0,
            stops: [20, 180]
        }
    },
    markers: {
        size: 0
    },
    tooltip: {
        enabled: false
    }
}