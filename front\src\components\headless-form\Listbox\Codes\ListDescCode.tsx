import {
  Description,
  Field,
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from "@headlessui/react";
import { useState } from "react";
import { Icon } from "@iconify/react";

const people = [
  { id: 1, name: "<PERSON><PERSON><PERSON>", available: true },
  { id: 2, name: "Kenton <PERSON>e", available: true },
  { id: 3, name: "<PERSON><PERSON>", available: false },
  { id: 4, name: "<PERSON>", available: false },
  { id: 5, name: "<PERSON><PERSON>", available: true },
];

const ListDescCode = () => {
  const [selectedPerson, setSelectedPerson] = useState(people[0]);
  return (
     
    <>
    <div className="mt-4 ">
      <Field className="w-full">
          <Description className="text-xs mb-2">
            This person will have full access to this project. This person will
            have full access to this project.
          </Description>
          <Listbox value={selectedPerson} onChange={setSelectedPerson}>
            <ListboxButton className="ui-button bg-warning  justify-between items-center gap-3 w-fit">
              {selectedPerson.name}{" "}
              <Icon icon="solar:alt-arrow-down-outline" height={18} />
            </ListboxButton>
            <ListboxOptions anchor="bottom" className="ui-dropdown">
              {people.map((person) => (
                <ListboxOption
                  key={person.id}
                  value={person}
                  className="ui-dropdown-item"
                >
                  {person.name}
                </ListboxOption>
              ))}
            </ListboxOptions>
          </Listbox>
        </Field>
    </div>
    </>
  )
}

export default ListDescCode
