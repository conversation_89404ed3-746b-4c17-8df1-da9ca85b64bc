
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from "@headlessui/react";
import { useState } from "react";
import CardBox from "src/components/shared/CardBox";
const people = [
  { id: 1, name: "<PERSON><PERSON><PERSON>" },
  { id: 2, name: "Kenton <PERSON>e" },
  { id: 3, name: "<PERSON><PERSON>" },
  { id: 4, name: "<PERSON>" },
  { id: 5, name: "<PERSON><PERSON>" },
];

const ListboxWithMultipleVal = () => {
  const [selectedPeople, setSelectedPeople] = useState([people[0], people[1]]);
  return (
    <div>
      <CardBox>
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-lg font-semibold">Selecting Multiple Values</h4>
        </div>
        <Listbox value={selectedPeople} onChange={setSelectedPeople} multiple>
          <ListboxButton className="ui-button bg-success justify-between items-center gap-3 w-fit">
            {selectedPeople.map((person) => person.name).join(", ")}
        
          </ListboxButton>
          <ListboxOptions anchor="bottom" className="origin-top ui-dropdown">
            {people.map((person) => (
              <ListboxOption
                key={person.id}
                value={person}
                className="ui-dropdown-item"
              >
                {person.name}
              </ListboxOption>
            ))}
          </ListboxOptions>
        </Listbox>
      </CardBox>
    </div>
  );
};

export default ListboxWithMultipleVal;
