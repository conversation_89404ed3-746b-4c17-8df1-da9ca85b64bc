// src/components/front-pages/homepage/StickyScrollImages.tsx
import { useRef, useContext, useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

// --- IMAGES FOR DESKTOP (HORIZONTAL VIEW) ---
import horizontalLeftImg from '/src/assets/images/front-pages/background/ContainerL.png';
import horizontalRightImg from '/src/assets/images/front-pages/background/ContainerR.png';

// --- IMAGES FOR MOBILE/TABLET (VERTICAL VIEW) ---
// Note: You can change these to different images for the vertical animation
import verticalTopImg from '/src/assets/images/front-pages/background/ContainerR-m.png'; 
import verticalBottomImg from '/src/assets/images/front-pages/background/ContainerL-m.png';

import { PageContext } from 'src/context/PageContext';

// Tailwind's default 'lg' breakpoint is 1024px. We'll use this for our check.
const LARGE_SCREEN_BREAKPOINT = 1024;

const StickyScrollImages = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const pageContext = useContext(PageContext);
  
  // State to determine which animation to use based on screen width
  const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth >= LARGE_SCREEN_BREAKPOINT);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end end'],
  });

  // --- HORIZONTAL ANIMATION (for Desktop) ---
  const xTransformLeft = useTransform(scrollYProgress, [0, 1], ['0%', '-50%']);
  const xTransformRight = useTransform(scrollYProgress, [0, 1], ['0%', '50%']);

  // --- VERTICAL ANIMATION (for Mobile/Tablet) ---
  const yTransformTop = useTransform(scrollYProgress, [0, 1], ['0%', '-50%']);
  const yTransformBottom = useTransform(scrollYProgress, [0, 1], ['0%', '50%']);

  const opacity = useTransform(scrollYProgress, [0, 0.8, 1], [1, 1, 0]);

  // Effect to update the screen size state on window resize
  useEffect(() => {
    const handleResize = () => {
        setIsLargeScreen(window.innerWidth >= LARGE_SCREEN_BREAKPOINT);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const unsubscribe = scrollYProgress.on('change', (latest) => {
      if (pageContext?.setHeaderVisible) {
        pageContext.setHeaderVisible(latest >= 0.9);
      }
    });
    return () => unsubscribe();
  }, [scrollYProgress, pageContext]);

  return (
    <div ref={containerRef} className="relative h-[200vh] bg-lightgray dark:bg-darkgray">
      <div className="sticky top-0 h-screen overflow-hidden">
        {/* This container is responsive: vertical on small screens, horizontal on large */}
        <div className="flex lg:flex-row flex-col">

          {/* Top / Left Image */}
          <motion.div
            style={isLargeScreen ? { x: xTransformLeft, opacity } : { y: yTransformTop, opacity }}
            className="lg:w-[60%] h-[45%]"
          >
            <img 
              src={isLargeScreen ? horizontalLeftImg : verticalTopImg} 
              alt="Feature visual" 
              className="w-[100%] h-[45%]"
            />
          </motion.div>
          
          {/* Bottom / Right Image */}
          <motion.div
            style={isLargeScreen ? { x: xTransformRight, opacity } : { y: yTransformBottom, opacity }}
            className="lg:w-[60%] h-[30%]"
          >
            <img 
              src={isLargeScreen ? horizontalRightImg : verticalBottomImg} 
              alt="Feature visual" 
              className="lg:w-[100%] h-[30%]"
            />
          </motion.div>

        </div>
      </div>
    </div>
  );
};

export default StickyScrollImages;