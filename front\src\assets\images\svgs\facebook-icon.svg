<svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2220 2220" width="18" height="18">
	<title>facebook-icon-svg</title>
	<defs>
		<clipPath clipPathUnits="userSpaceOnUse" id="cp1">
			<path d="m-2 5h2224v2210h-2224z"/>
		</clipPath>
		<image width="222" height="221" id="img1" href="data:image/png;base64,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"/>
	</defs>
	<style>
	</style>
	<g id="Clip-Path" clip-path="url(#cp1)">
		<g id="Layer">
			<use id="Layer" href="#img1" transform="matrix(10.018,0,0,10,-2,5)"/>
		</g>
	</g>
</svg>